import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    target: 'es2022',
    lib: {
      entry: resolve(__dirname, 'index.ts'),
      name: 'DLEngine',
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      external: [
        'react',
        'react-dom',
        'three',
        'bitecs',
        '@hookstate/core'
      ],
      output: {
        globals: {
          'react': 'React',
          'react-dom': 'ReactDOM',
          'three': 'THREE',
          'bitecs': 'bitECS'
        }
      }
    },
    sourcemap: true,
    minify: false
  },
  resolve: {
    alias: {
      '@dl-engine/engine-core': resolve(__dirname, './engine/core/src'),
      '@dl-engine/engine-ecs': resolve(__dirname, './engine/ecs/src'),
      '@dl-engine/engine-physics': resolve(__dirname, './engine/physics/src'),
      '@dl-engine/engine-state': resolve(__dirname, './engine/state/src'),
      '@dl-engine/shared-common': resolve(__dirname, './shared/common/src')
    }
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./test/setup.ts']
  }
})
