# Digital Learning Engine (DL-Engine)

## 项目概述

Digital Learning Engine (DL-Engine) 是基于现有的 Infinite Reality Engine 项目重构的数字化学习引擎，采用现代化的分布式微服务架构，专注于数字化学习和教育场景的3D/VR/AR应用开发平台。

## 核心特性

- 🎓 **教育场景优化**: 专为教育场景设计的3D/VR/AR引擎
- 🌐 **中文优先界面**: 支持手机号码登录的中文本地化界面
- 🏗️ **微服务架构**: 分布式微服务架构，支持水平扩展
- 🎮 **ECS游戏引擎**: 基于Entity-Component-System的高性能游戏引擎
- 📱 **多平台支持**: 支持VR/AR/桌面/移动端多平台部署
- 🤖 **AI集成**: 集成AI能力和边缘计算支持

## 技术栈

### 前端技术
- **React 18.2.0** + **TypeScript 5.6.3**
- **Three.js 0.176.0** - 3D渲染引擎
- **Vite 5.4.8** - 构建工具
- **Ant Design 5.0** - UI组件库
- **ECS架构** - 实体组件系统

### 后端技术
- **Node.js 22** + **Nest.js** + **FeathersJS**
- **MySQL** + **Redis** + **PostgreSQL**
- **Primus WebSocket** + **MediaSoup**
- **Rapier3D 0.11.2** - 物理引擎
- **Ollama** - AI集成
- **Minio** - 对象存储

### 部署技术
- **Kubernetes** + **Helm** + **Agones**
- **Docker Compose** + 边缘计算支持

## 项目结构

```
dl-engine/                          # 项目根目录
├── engine/                         # 底层引擎 (120,000行)
│   ├── core/                      # 核心引擎 (45,000行)
│   ├── ecs/                       # ECS系统 (25,000行)
│   ├── physics/                   # 物理引擎 (30,000行)
│   ├── state/                     # 状态管理 (20,000行)
│   ├── xr/                        # XR支持 (15,000行)
│   └── ai/                        # AI集成 (5,000行)
├── editor/                        # 在线编辑器 (85,000行)
├── server/                        # 服务器端 (180,000行)
├── client/                        # 客户端应用 (15,000行)
└── shared/                        # 共享模块 (14,232行)
```

## 快速开始

### 环境要求

- Node.js >= 22.0.0
- pnpm >= 9.0.0
- TypeScript 5.6.3

### 安装依赖

```bash
# 安装根目录依赖
pnpm install

# 安装所有子包依赖
pnpm -r install
```

### 开发模式

```bash
# 启动开发服务器
pnpm dev

# 构建项目
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint
```

## 开发指南

### 代码规范

- 使用 TypeScript 5.6.3 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 进行代码格式化
- 采用 Conventional Commits 提交规范

### 测试策略

- 单元测试覆盖率 > 80%
- 使用 Vitest 进行单元测试
- 使用 Playwright 进行端到端测试

## 重构进度

### 第一批次：底层引擎核心重构 ✅ 进行中
- [x] 项目基础架构搭建
- [ ] ECS系统完整迁移
- [ ] 核心渲染引擎迁移
- [ ] 物理引擎集成
- [ ] 状态管理系统

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 许可证

本项目采用 CPAL 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

- 项目主页: [dl-engine.org](https://dl-engine.org)
- 邮箱: <EMAIL>
- 问题反馈: [GitHub Issues](https://github.com/dl-engine/dl-engine/issues)
