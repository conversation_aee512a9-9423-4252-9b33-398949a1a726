{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@dl-engine/*": ["./*/src", "./*"], "@dl-engine/engine-core": ["./engine/core/src"], "@dl-engine/engine-ecs": ["./engine/ecs/src"], "@dl-engine/engine-physics": ["./engine/physics/src"], "@dl-engine/engine-state": ["./engine/state/src"], "@dl-engine/shared-common": ["./shared/common/src"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.d.ts"], "exclude": ["node_modules", "dist", "build", "coverage"], "references": [{"path": "./engine/core"}, {"path": "./engine/ecs"}, {"path": "./engine/physics"}, {"path": "./engine/state"}, {"path": "./shared/common"}]}