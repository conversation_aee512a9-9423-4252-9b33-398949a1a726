/**
 * DL-Engine 初始化函数
 * 引擎启动和配置
 */

import { createEngine, Engine } from '@dl-engine/engine-ecs'
import { getMutableState } from '@dl-engine/engine-state'
import { EngineState, EngineMode } from '@dl-engine/engine-ecs'
import { RendererManager, RendererConfig } from './rendering/RendererManager'

/**
 * DL-Engine 初始化配置
 */
export interface DLEngineConfig {
  /** 引擎模式 */
  mode?: EngineMode
  
  /** 渲染器配置 */
  renderer?: RendererConfig
  
  /** 是否启用调试模式 */
  debug?: boolean
  
  /** 是否启用详细日志 */
  verbose?: boolean
  
  /** 目标帧率 */
  targetFPS?: number
  
  /** 最大增量时间 */
  maxDeltaTime?: number
  
  /** 是否启用性能分析 */
  profiling?: boolean
  
  /** 教育模式配置 */
  education?: {
    enabled: boolean
    courseID?: string
    studentID?: string
    teacherID?: string
    classroomID?: string
  }
}

/**
 * 初始化DL-Engine
 * @param config 配置选项
 * @returns Promise<Engine>
 */
export async function initializeDLEngine(config: DLEngineConfig = {}): Promise<Engine> {
  console.log('Initializing Digital Learning Engine...')
  
  try {
    // 1. 创建ECS引擎
    const engine = createEngine()
    
    // 2. 配置引擎状态
    const engineState = getMutableState(EngineState)
    
    // 设置引擎模式
    if (config.mode) {
      engineState.setMode(config.mode)
    }
    
    // 设置调试模式
    if (config.debug !== undefined) {
      engineState.debugMode.set(config.debug)
    }
    
    // 设置详细日志
    if (config.verbose !== undefined) {
      engineState.verboseLogging.set(config.verbose)
    }
    
    // 设置教育模式
    if (config.education) {
      engineState.setEducationMode(config.education)
    }
    
    // 3. 初始化渲染器
    const rendererManager = RendererManager.getInstance()
    await rendererManager.initialize(config.renderer || {})
    
    // 4. 配置ECS系统
    if (config.targetFPS) {
      // 这里需要ECS状态的配置
      // getMutableState(ECSState).setTargetFPS(config.targetFPS)
    }
    
    if (config.maxDeltaTime) {
      // getMutableState(ECSState).maxDeltaSeconds.set(config.maxDeltaTime)
    }
    
    if (config.profiling) {
      // getMutableState(ECSState).setPerformanceProfiling(config.profiling)
    }
    
    // 5. 注册核心系统
    await registerCoreSystems()
    
    // 6. 标记为已初始化
    engineState.setInitialized(true)
    
    console.log('Digital Learning Engine initialized successfully')
    console.log(`Mode: ${engineState.mode.value}`)
    console.log(`Debug: ${engineState.debugMode.value}`)
    console.log(`Education Mode: ${engineState.education.isEducationMode.value}`)
    
    return engine
    
  } catch (error) {
    console.error('Failed to initialize DL-Engine:', error)
    throw error
  }
}

/**
 * 注册核心系统
 */
async function registerCoreSystems(): Promise<void> {
  // 这里将注册各种核心系统
  // 包括渲染系统、动画系统、物理系统等
  
  console.log('Registering core systems...')
  
  // TODO: 实现系统注册
  // registerRenderSystem()
  // registerAnimationSystem()
  // registerPhysicsSystem()
  // registerInputSystem()
  // registerAudioSystem()
  
  console.log('Core systems registered')
}

/**
 * 销毁DL-Engine
 */
export function destroyDLEngine(): void {
  console.log('Destroying Digital Learning Engine...')
  
  try {
    // 1. 销毁渲染器
    const rendererManager = RendererManager.getInstance()
    rendererManager.dispose()
    
    // 2. 销毁ECS引擎
    // destroyEngine() // 从engine-ecs导入
    
    console.log('Digital Learning Engine destroyed successfully')
    
  } catch (error) {
    console.error('Error destroying DL-Engine:', error)
    throw error
  }
}

/**
 * 重启DL-Engine
 * @param config 新的配置选项
 */
export async function restartDLEngine(config?: DLEngineConfig): Promise<Engine> {
  console.log('Restarting Digital Learning Engine...')
  
  destroyDLEngine()
  return await initializeDLEngine(config)
}

/**
 * 检查DL-Engine是否已初始化
 */
export function isDLEngineInitialized(): boolean {
  try {
    return Engine.isInitialized()
  } catch {
    return false
  }
}

/**
 * 获取DL-Engine信息
 */
export function getDLEngineInfo() {
  if (!isDLEngineInitialized()) {
    return {
      initialized: false,
      name: 'Digital Learning Engine',
      version: '1.0.0'
    }
  }
  
  const engineState = EngineState.value
  const rendererManager = RendererManager.getInstance()
  
  return {
    initialized: true,
    name: engineState.name,
    version: engineState.version,
    mode: engineState.mode,
    platform: engineState.platform,
    uptime: engineState.uptime,
    features: engineState.features,
    renderer: {
      hasRenderer: !!rendererManager.getRenderer(),
      hasScene: !!rendererManager.getScene(),
      hasCamera: !!rendererManager.getCamera()
    },
    education: engineState.education
  }
}

/**
 * 预设配置
 */
export const DLEnginePresets = {
  /**
   * 开发模式配置
   */
  development: (): DLEngineConfig => ({
    mode: EngineMode.DEVELOPMENT,
    debug: true,
    verbose: true,
    profiling: true,
    targetFPS: 60,
    renderer: {
      antialias: true,
      powerPreference: 'high-performance'
    }
  }),
  
  /**
   * 生产模式配置
   */
  production: (): DLEngineConfig => ({
    mode: EngineMode.PRODUCTION,
    debug: false,
    verbose: false,
    profiling: false,
    targetFPS: 60,
    renderer: {
      antialias: true,
      powerPreference: 'default'
    }
  }),
  
  /**
   * 教育模式配置
   */
  education: (courseID?: string, studentID?: string): DLEngineConfig => ({
    mode: EngineMode.PRODUCTION,
    debug: false,
    verbose: false,
    targetFPS: 60,
    education: {
      enabled: true,
      courseID,
      studentID
    },
    renderer: {
      antialias: true,
      powerPreference: 'default'
    }
  }),
  
  /**
   * 编辑器模式配置
   */
  editor: (): DLEngineConfig => ({
    mode: EngineMode.EDITOR,
    debug: true,
    verbose: true,
    profiling: true,
    targetFPS: 60,
    renderer: {
      antialias: true,
      powerPreference: 'high-performance',
      preserveDrawingBuffer: true
    }
  }),
  
  /**
   * VR模式配置
   */
  vr: (): DLEngineConfig => ({
    mode: EngineMode.PRODUCTION,
    debug: false,
    verbose: false,
    targetFPS: 90, // VR通常需要更高帧率
    renderer: {
      antialias: false, // VR中通常关闭抗锯齿以提高性能
      powerPreference: 'high-performance',
      xr: true
    }
  }),
  
  /**
   * 移动设备配置
   */
  mobile: (): DLEngineConfig => ({
    mode: EngineMode.PRODUCTION,
    debug: false,
    verbose: false,
    targetFPS: 30, // 移动设备通常使用较低帧率
    maxDeltaTime: 1 / 20, // 更宽松的最大增量时间
    renderer: {
      antialias: false, // 移动设备关闭抗锯齿
      powerPreference: 'low-power',
      pixelRatio: Math.min(window.devicePixelRatio, 2) // 限制像素比
    }
  })
}
